#!/usr/bin/env python3
"""
Скрипт для тестирования производительности функций микротем
"""
import asyncio
import time
from common.image_utils import generate_microtopics_table_image

async def test_image_generation_performance():
    """Тестирует производительность генерации изображений"""
    
    # Тестовые данные контрольного теста с сравнением
    control_test_data = {}
    microtopic_names = {}
    
    # Генерируем большой набор данных для тестирования
    for i in range(1, 51):  # 50 микротем
        control_test_data[i] = {
            'comparison_text': f'{i*2}% → {i*2+10}% 📈',
            'control_percentage': i*2+10,
            'entry_percentage': i*2
        }
        microtopic_names[i] = f'Микротема {i}: Тестовая тема номер {i}'
    
    title = 'Контрольный тест месяца: Тестовый тест\\nПредмет: Математика\\nСравнение с входным тестом'
    
    print("🧪 Тестирование производительности генерации изображений...")
    
    # Тест 1: Детальный режим
    start_time = time.time()
    try:
        image_bytes = await generate_microtopics_table_image(
            microtopic_data=control_test_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode='detailed',
            data_source='control_test',
            page=0,
            items_per_page=15
        )
        end_time = time.time()
        print(f"✅ Детальный режим: {end_time - start_time:.2f}с (размер: {len(image_bytes)} байт)")
    except Exception as e:
        print(f"❌ Ошибка в детальном режиме: {e}")
    
    # Тест 2: Режим сводки
    start_time = time.time()
    try:
        image_bytes = await generate_microtopics_table_image(
            microtopic_data=control_test_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode='summary',
            data_source='control_test',
            page=0,
            items_per_page=15
        )
        end_time = time.time()
        print(f"✅ Режим сводки: {end_time - start_time:.2f}с (размер: {len(image_bytes)} байт)")
    except Exception as e:
        print(f"❌ Ошибка в режиме сводки: {e}")

async def test_database_simulation():
    """Симулирует запросы к базе данных"""
    
    print("🗄️ Тестирование симуляции запросов к БД...")
    
    # Симулируем последовательные запросы
    start_time = time.time()
    for i in range(8):  # 8 запросов как в оригинальной функции
        await asyncio.sleep(0.1)  # Симулируем задержку БД
    end_time = time.time()
    print(f"📊 Последовательные запросы: {end_time - start_time:.2f}с")
    
    # Симулируем параллельные запросы
    start_time = time.time()
    tasks = []
    for i in range(8):
        tasks.append(asyncio.sleep(0.1))
    await asyncio.gather(*tasks)
    end_time = time.time()
    print(f"⚡ Параллельные запросы: {end_time - start_time:.2f}с")

async def main():
    """Основная функция тестирования"""
    print("🚀 Запуск тестов производительности...")
    print("=" * 50)
    
    await test_database_simulation()
    print()
    await test_image_generation_performance()
    
    print("=" * 50)
    print("✅ Тестирование завершено!")

if __name__ == "__main__":
    asyncio.run(main())
