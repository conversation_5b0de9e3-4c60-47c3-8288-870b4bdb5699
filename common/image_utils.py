from PIL import Image, ImageDraw, ImageFont
from pilmoji import <PERSON><PERSON><PERSON><PERSON>
from typing import Dict, List, Tuple, Union
import io


def normalize_microtopic_data(microtopic_data: Dict, data_source: str = "student") -> Dict[int, Union[float, str]]:
    """
    Нормализует данные о микротемах к единому формату {microtopic_number: percentage_or_comparison}

    Args:
        microtopic_data: Данные в любом формате
        data_source: Источник данных ("student", "group", "subject", "test", "control_test")

    Returns:
        Dict[int, Union[float, str]]: {номер_микротемы: процент_или_сравнение}
    """
    normalized = {}

    for microtopic_num, data in microtopic_data.items():
        if data_source == "student":
            # Формат: {'percentage': 85, 'total_answered': 10, 'correct_answered': 8}
            if isinstance(data, dict) and 'percentage' in data:
                normalized[microtopic_num] = float(data['percentage'])
        elif data_source in ["group", "subject"]:
            # Формат: [percentage1, percentage2, ...] - массив процентов от разных студентов
            if isinstance(data, list) and data:
                normalized[microtopic_num] = round(sum(data) / len(data), 1)
        elif data_source == "test":
            # Формат: {'percentage': 75, 'correct': 3, 'total': 4}
            if isinstance(data, dict) and 'percentage' in data:
                normalized[microtopic_num] = float(data['percentage'])
        elif data_source == "control_test":
            # Формат: {'comparison_text': '33% → 66% 📈', 'control_percentage': 66, 'entry_percentage': 33}
            if isinstance(data, dict) and 'comparison_text' in data:
                normalized[microtopic_num] = data['comparison_text']  # Возвращаем готовый текст сравнения
        else:
            # Попытка автоматического определения формата
            if isinstance(data, dict) and 'percentage' in data:
                normalized[microtopic_num] = float(data['percentage'])
            elif isinstance(data, list) and data:
                normalized[microtopic_num] = round(sum(data) / len(data), 1)
            elif isinstance(data, (int, float)):
                normalized[microtopic_num] = float(data)

    return normalized


def get_status_icon(percentage: float) -> str:
    """Получить иконку статуса по проценту"""
    if percentage >= 80:
        return "✅"
    elif percentage <= 40:
        return "❌"
    else:
        return "⚠️"


def replace_emojis_for_performance(text: str) -> str:
    """Заменяет эмодзи на простые символы для ускорения отрисовки"""
    replacements = {
        '📈': '[UP]',
        '📉': '[DOWN]',
        '➡️': '[=]',
        '✅': '[+]',
        '❌': '[-]',
        '⚠️': '[~]',
        '📊': '[CHART]',
        '📗': '[BOOK]',
        '💪': '[STRONG]',
        '🟢': '[GREEN]',
        '🔴': '[RED]'
    }

    result = text
    for emoji, replacement in replacements.items():
        result = result.replace(emoji, replacement)

    return result


def get_status_color(percentage: float) -> Tuple[int, int, int]:
    """Получить цвет статуса по проценту (RGB) - пастельные цвета"""
    if percentage >= 80:
        return (144, 238, 144)  # Светло-зеленый (пастельный)
    elif percentage <= 40:
        return (255, 182, 193)  # Светло-розовый (пастельный)
    else:
        return (255, 218, 185)  # Персиковый (пастельный)


async def generate_microtopics_table_image(
    microtopic_data: Dict,
    microtopic_names: Dict[int, str],
    title: str,
    display_mode: str = "detailed",
    data_source: str = "student",
    page: int = 0,
    items_per_page: int = 30
) -> bytes:
    """
    Генерирует изображение таблицы с пониманием по микротемам
    
    Args:
        microtopic_data: Данные по микротемам в любом формате
        microtopic_names: {номер: название} микротем
        title: Заголовок таблицы
        display_mode: "detailed" (все микротемы) или "summary" (только сильные/слабые)
        data_source: Источник данных ("student", "group", "subject", "test")
        page: Номер страницы (для пагинации)
        items_per_page: Количество элементов на странице
    
    Returns:
        bytes: Изображение в формате PNG
    """
    # Нормализуем данные
    normalized_data = normalize_microtopic_data(microtopic_data, data_source)
    
    if not normalized_data:
        # Создаем изображение с сообщением об отсутствии данных
        return create_no_data_image(title)

    # Фильтруем данные в зависимости от режима отображения
    if display_mode == "summary":
        # Создаем изображение сводки с разделением на сильные и слабые темы
        return create_summary_image(normalized_data, microtopic_names, title, page, items_per_page)
    else:
        filtered_data = normalized_data

    if not filtered_data:
        return create_no_data_image(title, "Нет данных для отображения")

    # Сортируем по номеру микротемы
    sorted_items = sorted(filtered_data.items())

    # Применяем пагинацию
    start_idx = page * items_per_page
    end_idx = start_idx + items_per_page
    page_items = sorted_items[start_idx:end_idx]

    if not page_items:
        return create_no_data_image(title, "Страница не найдена")

    # Создаем изображение
    return create_table_image(page_items, microtopic_names, title, page, len(sorted_items), items_per_page)


def create_no_data_image(title: str, message: str = "❌ Нет данных по микротемам") -> bytes:
    """Создает изображение с сообщением об отсутствии данных"""
    width, height = 800, 250
    img = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(img)

    try:
        title_font = ImageFont.truetype("arial.ttf", 18)
        message_font = ImageFont.truetype("arial.ttf", 16)
    except:
        title_font = ImageFont.load_default()
        message_font = ImageFont.load_default()

    # Используем Pilmoji для рендеринга эмодзи
    with Pilmoji(img) as pilmoji:
        # Рисуем заголовок с эмодзи
        pilmoji.text((20, 20), title, fill='black', font=title_font)

        # Вычисляем позицию для центрирования сообщения
        # Получаем размеры текста для центрирования
        bbox = draw.textbbox((0, 0), message, font=message_font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # Центрируем по горизонтали и размещаем в центре изображения
        x = (width - text_width) // 2
        y = (height - text_height) // 2 + 20  # Немного ниже центра, чтобы учесть заголовок

        # Рисуем сообщение с эмодзи (серый цвет вместо красного)
        pilmoji.text((x, y), message, fill='gray', font=message_font)

    # Конвертируем в байты
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    return img_bytes.getvalue()


def create_empty_summary_image(title: str) -> bytes:
    """Создает изображение для случая, когда нет ни сильных, ни слабых тем"""
    width, height = 800, 200
    img = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(img)

    try:
        title_font = ImageFont.truetype("arial.ttf", 18)
        message_font = ImageFont.truetype("arial.ttf", 16)
    except:
        title_font = ImageFont.load_default()
        message_font = ImageFont.load_default()

    # Используем Pilmoji для рендеринга эмодзи
    with Pilmoji(img) as pilmoji:
        # Рисуем заголовок с эмодзи
        pilmoji.text((20, 20), title, fill='black', font=title_font)

        # Сообщение для центрирования
        message = "📊 Нет сильных или слабых тем"

        # Вычисляем позицию для центрирования сообщения
        bbox = draw.textbbox((0, 0), message, font=message_font)
        text_width = bbox[2] - bbox[0]

        # Центрируем по горизонтали и размещаем в центре изображения
        x = (width - text_width) // 2
        y = 120  # Фиксированная позиция в центре

        # Рисуем сообщение с эмодзи (черный цвет, нормальный размер)
        pilmoji.text((x, y), message, fill='black', font=message_font)

    # Конвертируем в байты
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    return img_bytes.getvalue()


def create_table_image(
    items: List[Tuple[int, Union[float, str]]],
    microtopic_names: Dict[int, str],
    title: str,
    page: int,
    total_items: int,
    items_per_page: int
) -> bytes:
    """Создает изображение таблицы с микротемами"""

    # Настройки
    padding = 20
    row_height = 35
    header_height = 40
    title_height = 80  # Увеличили для отступа
    title_bottom_margin = 20  # Отступ между заголовком и таблицей

    # Вычисляем размеры
    table_width = 700
    table_height = len(items) * row_height + header_height
    total_height = title_height + title_bottom_margin + table_height + padding * 2

    # Добавляем место для информации о пагинации
    if total_items > items_per_page:
        total_height += 30

    img = Image.new('RGB', (table_width + padding * 2, total_height), color='white')
    draw = ImageDraw.Draw(img)

    try:
        title_font = ImageFont.truetype("arial.ttf", 18)
        header_font = ImageFont.truetype("arial.ttf", 14)
        cell_font = ImageFont.truetype("arial.ttf", 12)
    except:
        title_font = ImageFont.load_default()
        header_font = ImageFont.load_default()
        cell_font = ImageFont.load_default()
    
    # Полностью оптимизированная версия без pilmoji
    draw = ImageDraw.Draw(img)
    y_offset = padding

    # Рисуем заголовок с эмодзи (оптимизированно)
    with Pilmoji(img) as pilmoji:
        pilmoji.text((padding, y_offset), title, fill='black', font=title_font)
    y_offset += title_height + title_bottom_margin

    # Рисуем информацию о пагинации (без эмодзи - используем обычный draw)
    if total_items > items_per_page:
        total_pages = (total_items + items_per_page - 1) // items_per_page
        page_info = f"Страница {page + 1} из {total_pages} (всего микротем: {total_items})"
        draw.text((padding, y_offset), page_info, fill='gray', font=cell_font)
        y_offset += 30

    # Рисуем заголовки таблицы
    header_y = y_offset

    # Заголовок таблицы с пастельным цветом (без рамки)
    draw.rectangle([padding, header_y, padding + table_width, header_y + header_height],
                  fill=(230, 230, 250))  # Лавандовый пастельный, без outline

    # Разделитель колонок - уменьшили ширину для микротем
    col1_width = int(table_width * 0.6)  # 60% для названия микротемы (было 70%)
    draw.line([padding + col1_width, header_y, padding + col1_width, header_y + header_height],
              fill='lightgray', width=1)

    # Текст заголовков (без эмодзи - используем обычный draw)
    draw.text((padding + 10, header_y + 10), "Микротема", fill='black', font=header_font)
    draw.text((padding + col1_width + 10, header_y + 10), "Результат", fill='black', font=header_font)

    y_offset += header_height

    # Рисуем строки данных (оптимизированно)
    emoji_texts = []  # Собираем тексты с эмодзи для batch-обработки

    for i, (microtopic_num, percentage) in enumerate(items):
        row_y = y_offset + i * row_height

        # Цвет фона строки
        bg_color = (255, 255, 255) if i % 2 == 0 else (240, 240, 250)
        draw.rectangle([padding, row_y, padding + table_width, row_y + row_height],
                      fill=bg_color)

        # Разделитель колонок
        draw.line([padding + col1_width, row_y, padding + col1_width, row_y + row_height],
                  fill='lightgray', width=1)

        # Название микротемы (без эмодзи - используем обычный draw)
        microtopic_name = microtopic_names.get(microtopic_num, f"Микротема {microtopic_num}")
        if len(microtopic_name) > 35:
            microtopic_name = microtopic_name[:32] + "..."

        draw.text((padding + 10, row_y + 8), microtopic_name, fill='black', font=cell_font)

        # Обрабатываем данные в зависимости от типа
        if isinstance(percentage, str):
            # Для контрольных тестов - готовый текст сравнения (содержит эмодзи)
            result_text = percentage
            text_color = 'black'
            # Добавляем в список для обработки с pilmoji
            emoji_texts.append((padding + col1_width + 10, row_y + 8, result_text, text_color))
        else:
            # Для обычных данных - процент и иконка статуса (содержит эмодзи)
            status_icon = get_status_icon(percentage)
            result_text = f"{percentage:.0f}% {status_icon}"

            # Цвет текста в зависимости от процента
            if percentage >= 80:
                text_color = (0, 128, 0)  # Зеленый текст
            elif percentage <= 40:
                text_color = (220, 20, 60)  # Красный текст
            else:
                text_color = (255, 140, 0)  # Оранжевый текст

            # Добавляем в список для обработки с pilmoji
            emoji_texts.append((padding + col1_width + 10, row_y + 8, result_text, text_color))

    # Возвращаем эмодзи, но с оптимизацией (batch-обработка)
    if emoji_texts:
        with Pilmoji(img) as pilmoji:
            for x, y, text, color in emoji_texts:
                pilmoji.text((x, y), text, fill=color, font=cell_font)
    
        # Убираем рамку вокруг всей таблицы

    # Конвертируем в байты
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    return img_bytes.getvalue()


def create_summary_image(
    normalized_data: Dict[int, Union[float, str]],
    microtopic_names: Dict[int, str],
    title: str,
    page: int = 0,
    items_per_page: int = 15
) -> bytes:
    """Создает изображение сводки с разделением на сильные и слабые темы с пагинацией"""

    # Разделяем темы на сильные и слабые
    # Для контрольных тестов используем другую логику разделения
    strong_topics = {}
    weak_topics = {}

    for num, data in normalized_data.items():
        if isinstance(data, str):
            # Для контрольных тестов анализируем текст сравнения
            if "📈" in data or ("→" in data and "100%" in data.split("→")[1]):
                # Улучшение или высокий результат
                strong_topics[num] = data
            elif "📉" in data or ("→" in data and any(x in data.split("→")[1] for x in ["0%", "1%", "2%", "3%"])):
                # Ухудшение или низкий результат
                weak_topics[num] = data
        else:
            # Для обычных данных используем стандартную логику
            if data >= 80:
                strong_topics[num] = data
            elif data <= 40:
                weak_topics[num] = data

    # Сортируем по номеру микротемы
    all_strong_items = sorted(strong_topics.items())
    all_weak_items = sorted(weak_topics.items())

    # Применяем пагинацию
    # Каждая страница содержит до items_per_page элементов из каждой категории
    strong_per_page = items_per_page // 2  # Половина на сильные темы
    weak_per_page = items_per_page - strong_per_page  # Остальное на слабые темы

    # Вычисляем индексы для текущей страницы
    strong_start = page * strong_per_page
    strong_end = strong_start + strong_per_page
    weak_start = page * weak_per_page
    weak_end = weak_start + weak_per_page

    # Получаем элементы для текущей страницы
    strong_items = all_strong_items[strong_start:strong_end]
    weak_items = all_weak_items[weak_start:weak_end]

    # Проверяем, есть ли данные для отображения на этой странице
    if not strong_items and not weak_items:
        if page == 0 and len(all_strong_items) == 0 and len(all_weak_items) == 0:
            # Нет вообще никаких данных - показываем специальное сообщение
            return create_empty_summary_image(title)
        else:
            # Не первая страница - показываем "Страница не найдена"
            return create_no_data_image(title, "Страница не найдена")

    # Вычисляем общее количество элементов для информации о пагинации
    total_strong = len(all_strong_items)
    total_weak = len(all_weak_items)
    max_pages_strong = (total_strong + strong_per_page - 1) // strong_per_page if total_strong > 0 else 0
    max_pages_weak = (total_weak + weak_per_page - 1) // weak_per_page if total_weak > 0 else 0
    total_pages = max(max_pages_strong, max_pages_weak, 1)

    # Настройки
    padding = 20
    row_height = 35
    header_height = 40
    section_header_height = 30
    title_height = 80
    title_bottom_margin = 20
    section_margin = 15  # Отступ между секциями

    # Вычисляем размеры
    table_width = 700

    # Высота для заголовка
    total_height = title_height + title_bottom_margin + padding * 2

    # Высота для секции сильных тем
    if strong_items:
        total_height += section_header_height + header_height + len(strong_items) * row_height + section_margin
    else:
        total_height += section_header_height + 30 + section_margin  # Место для "Нет данных"

    # Высота для секции слабых тем
    if weak_items:
        total_height += section_header_height + header_height + len(weak_items) * row_height
    else:
        total_height += section_header_height + 30  # Место для "Нет данных"

    # Добавляем место для информации о пагинации
    if total_pages > 1:
        total_height += 30

    img = Image.new('RGB', (table_width + padding * 2, total_height), color='white')
    draw = ImageDraw.Draw(img)

    try:
        title_font = ImageFont.truetype("arial.ttf", 18)
        section_font = ImageFont.truetype("arial.ttf", 16)
        header_font = ImageFont.truetype("arial.ttf", 14)
        cell_font = ImageFont.truetype("arial.ttf", 12)
    except:
        title_font = ImageFont.load_default()
        section_font = ImageFont.load_default()
        header_font = ImageFont.load_default()
        cell_font = ImageFont.load_default()

    # Используем Pilmoji для рендеринга всего изображения с эмодзи
    with Pilmoji(img) as pilmoji:
        y_offset = padding

        # Рисуем заголовок с эмодзи
        pilmoji.text((padding, y_offset), title, fill='black', font=title_font)
        y_offset += title_height + title_bottom_margin

        # Секция сильных тем
        pilmoji.text((padding, y_offset), "Сильные темы (≥80%):", fill='green', font=section_font)
        y_offset += section_header_height

        if strong_items:
            # Заголовок таблицы для сильных тем
            draw.rectangle([padding, y_offset, padding + table_width, y_offset + header_height],
                          fill=(144, 238, 144))  # Светло-зеленый фон

            col1_width = int(table_width * 0.6)
            draw.line([padding + col1_width, y_offset, padding + col1_width, y_offset + header_height],
                      fill='gray', width=1)

            pilmoji.text((padding + 10, y_offset + 10), "Тема", fill='black', font=header_font)
            pilmoji.text((padding + col1_width + 10, y_offset + 10), "Процент понимания", fill='black', font=header_font)
            y_offset += header_height

            # Строки данных для сильных тем
            for i, (microtopic_num, percentage) in enumerate(strong_items):
                row_y = y_offset + i * row_height

                bg_color = (255, 255, 255) if i % 2 == 0 else (240, 255, 240)  # Белый и светло-зеленый
                draw.rectangle([padding, row_y, padding + table_width, row_y + row_height], fill=bg_color)

                draw.line([padding + col1_width, row_y, padding + col1_width, row_y + row_height],
                          fill='gray', width=1)

                microtopic_name = microtopic_names.get(microtopic_num, f"Микротема {microtopic_num}")
                if len(microtopic_name) > 35:
                    microtopic_name = microtopic_name[:32] + "..."

                pilmoji.text((padding + 10, row_y + 8), microtopic_name, fill='black', font=cell_font)

                # Обрабатываем данные в зависимости от типа
                if isinstance(percentage, str):
                    # Для контрольных тестов - готовый текст сравнения
                    result_text = percentage
                else:
                    # Для обычных данных - процент и иконка статуса
                    status_icon = get_status_icon(percentage)
                    result_text = f"{percentage:.0f}% {status_icon}"

                pilmoji.text((padding + col1_width + 10, row_y + 8), result_text, fill=(0, 128, 0), font=cell_font)

            y_offset += len(strong_items) * row_height
        else:
            pilmoji.text((padding + 20, y_offset), "Нет сильных тем", fill='gray', font=cell_font)
            y_offset += 30

        y_offset += section_margin

        # Секция слабых тем
        pilmoji.text((padding, y_offset), "Слабые темы (≤40%):", fill='red', font=section_font)
        y_offset += section_header_height

        if weak_items:
            # Заголовок таблицы для слабых тем
            draw.rectangle([padding, y_offset, padding + table_width, y_offset + header_height],
                          fill=(255, 182, 193))  # Светло-розовый фон

            col1_width = int(table_width * 0.6)
            draw.line([padding + col1_width, y_offset, padding + col1_width, y_offset + header_height],
                      fill='gray', width=1)

            pilmoji.text((padding + 10, y_offset + 10), "Тема", fill='black', font=header_font)
            pilmoji.text((padding + col1_width + 10, y_offset + 10), "Процент понимания", fill='black', font=header_font)
            y_offset += header_height

            # Строки данных для слабых тем
            for i, (microtopic_num, percentage) in enumerate(weak_items):
                row_y = y_offset + i * row_height

                bg_color = (255, 255, 255) if i % 2 == 0 else (255, 240, 240)  # Белый и светло-розовый
                draw.rectangle([padding, row_y, padding + table_width, row_y + row_height], fill=bg_color)

                draw.line([padding + col1_width, row_y, padding + col1_width, row_y + row_height],
                          fill='gray', width=1)

                microtopic_name = microtopic_names.get(microtopic_num, f"Микротема {microtopic_num}")
                if len(microtopic_name) > 35:
                    microtopic_name = microtopic_name[:32] + "..."

                pilmoji.text((padding + 10, row_y + 8), microtopic_name, fill='black', font=cell_font)

                # Обрабатываем данные в зависимости от типа
                if isinstance(percentage, str):
                    # Для контрольных тестов - готовый текст сравнения
                    result_text = percentage
                else:
                    # Для обычных данных - процент и иконка статуса
                    status_icon = get_status_icon(percentage)
                    result_text = f"{percentage:.0f}% {status_icon}"

                pilmoji.text((padding + col1_width + 10, row_y + 8), result_text, fill=(220, 20, 60), font=cell_font)
        else:
            pilmoji.text((padding + 20, y_offset), "Нет слабых тем", fill='gray', font=cell_font)

        # Добавляем информацию о пагинации
        if total_pages > 1:
            y_offset = total_height - padding - 25
            pagination_text = f"Страница {page + 1} из {total_pages}"
            if total_strong > 0:
                strong_shown = len(strong_items)
                pagination_text += f" | Сильных: {strong_shown}/{total_strong}"
            if total_weak > 0:
                weak_shown = len(weak_items)
                pagination_text += f" | Слабых: {weak_shown}/{total_weak}"

            pilmoji.text((padding + 10, y_offset), pagination_text, fill='gray', font=cell_font)

    # Конвертируем в байты
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    return img_bytes.getvalue()
