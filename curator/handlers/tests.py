import logging
from aiogram import Router
from curator.states.states_tests import CuratorTestsStatisticsStates, STATE_TRANSITIONS, STATE_HANDLERS
from common.tests_statistics.register_handlers import register_test_statistics_handlers

# Настраиваем логгер
logger = logging.getLogger(__name__)

router = Router()

# Регистрируем обработчики для куратора
register_test_statistics_handlers(router, CuratorTestsStatisticsStates, "curator")

# Регистрируем обработчики микротем для входных тестов месяца куратора
from common.microtopics.register_handlers import register_curator_month_entry_test_microtopics_handlers
from common.tests_statistics.keyboards import get_back_kb

register_curator_month_entry_test_microtopics_handlers(
    router=router,
    states_group=CuratorTestsStatisticsStates,
    detailed_callback_prefix="curator_month_entry_page",
    summary_callback_prefix="curator_month_entry_summary_page",
    detailed_state=CuratorTestsStatisticsStates.month_entry_detailed_stats,
    summary_state=CuratorTestsStatisticsStates.month_entry_summary_stats,
    result_state=CuratorTestsStatisticsStates.month_entry_result_display,  # Не используется
    back_keyboard_func=get_back_kb,
    items_per_page_detailed=15,
    items_per_page_summary=15,
    premium_check=False  # Куратор имеет доступ к статистике своих студентов
)
